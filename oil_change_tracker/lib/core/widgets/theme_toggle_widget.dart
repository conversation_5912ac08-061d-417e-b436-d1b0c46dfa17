import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/theme_provider.dart';

class ThemeToggleWidget extends ConsumerStatefulWidget {
  const ThemeToggleWidget({super.key});

  @override
  ConsumerState<ThemeToggleWidget> createState() => _ThemeToggleWidgetState();
}

class _ThemeToggleWidgetState extends ConsumerState<ThemeToggleWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeModeProvider);
    final isDarkMode = themeMode == ThemeMode.dark ||
        (themeMode == ThemeMode.system &&
            MediaQuery.of(context).platformBrightness == Brightness.dark);

    // Update animation based on theme
    if (isDarkMode) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    return GestureDetector(
      onTap: () {
        final newThemeMode = isDarkMode ? AppThemeMode.light : AppThemeMode.dark;
        ref.read(themeModeProvider.notifier).setThemeMode(newThemeMode);
      },
      child: Container(
        width: 56,
        height: 32,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: isDarkMode 
              ? Colors.indigo.withOpacity(0.3)
              : Colors.orange.withOpacity(0.3),
          border: Border.all(
            color: isDarkMode ? Colors.indigo : Colors.orange,
            width: 1.5,
          ),
        ),
        child: Stack(
          children: [
            // Background icons
            Positioned.fill(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Icon(
                    Icons.wb_sunny,
                    size: 16,
                    color: isDarkMode 
                        ? Colors.orange.withOpacity(0.5)
                        : Colors.orange,
                  ),
                  Icon(
                    Icons.nightlight_round,
                    size: 16,
                    color: isDarkMode 
                        ? Colors.indigo
                        : Colors.indigo.withOpacity(0.5),
                  ),
                ],
              ),
            ),
            // Animated toggle circle
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Positioned(
                  left: _animation.value * 26 + 2,
                  top: 2,
                  child: Container(
                    width: 26,
                    height: 26,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      isDarkMode ? Icons.nightlight_round : Icons.wb_sunny,
                      size: 16,
                      color: isDarkMode ? Colors.indigo : Colors.orange,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
